package com.pugwoo.weboperate;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.HashMap;
import java.util.Map;

@Controller
public class HelloWorldController {

    @GetMapping("/vm")
    public String vm() {
        return "hello";
    }

    @ResponseBody @GetMapping("/api")
    public Map<String, Object> api() {
        Map<String, Object> map = new HashMap<>();
        map.put("name", "nick");
        map.put("age", 36);
        return map;
    }
}
