package com.pugwoo.dboperate.ola_analysis;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * ola表信息
 */
@Data
@ToString
@Table("ola_table_info")
public class OlaTableInfoDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Integer id;

    /** 软删除，0未删，1已删<br/>Column: [deleted] */
    @Column(value = "deleted", softDelete = {"0", "1"})
    private Integer deleted;

    /** 创建时间<br/>Column: [create_time] */
    @Column(value = "create_time", setTimeWhenInsert = true)
    private LocalDateTime createTime;

    /** 修改时间<br/>Column: [update_time] */
    @Column(value = "update_time", setTimeWhenInsert = true, setTimeWhenUpdate = true)
    private LocalDateTime updateTime;

    /** 数据库.表名<br/>Column: [table_full_name] */
    @Column(value = "table_full_name")
    private String tableFullName;

    /** 数据库名<br/>Column: [database_name] */
    @Column(value = "database_name")
    private String databaseName;

    /** 表名<br/>Column: [table_name] */
    @Column(value = "table_name")
    private String tableName;

    /** 插入数据的SQL，ola都是用insert来表达数据计算逻辑的<br/>Column: [insert_sql] */
    @Column(value = "insert_sql")
    private String insertSql;

    /** 表的建表sql<br/>Column: [table_ddl] */
    @Column(value = "table_ddl")
    private String tableDdl;

}