package com.pugwoo.dboperate.ola_analysis;

import com.pugwoo.dbhelper.DBHelper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@Slf4j
@SpringBootTest
public class QueryDependentTests {

    @Resource
    private DBHelper devcloudDevDBHelper;

    @Test
    public void testQueryDependents() {
        String table = "clickhouse_ck_demand.ads_tcres_demand_annual_excueted_report";

        OlaTableDependenceVO d = devcloudDevDBHelper.getOne(OlaTableDependenceVO.class,
                "where table_name=?", table);

        // 转换成staruml的图示来表达依赖关系
        /*
        @startuml
' 设置图的布局方向为从左到右
left to right direction

' 定义各个节点（可以使用类、组件或简单的实体）
node Root
node Child1
node Child2
node Grandchild1
node Grandchild2
node Grandchild3

' 定义节点之间的依赖关系
Root --> Child1
Root --> Child2
Child1 --> Grandchild1
Child1 --> Grandchild2
Child2 --> Grandchild3

@enduml
         */

        System.out.println(d);
    }
}
