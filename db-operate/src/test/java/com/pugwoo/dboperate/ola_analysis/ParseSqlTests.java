package com.pugwoo.dboperate.ola_analysis;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.io.IOUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.create.table.CreateTable;
import net.sf.jsqlparser.statement.insert.Insert;
import net.sf.jsqlparser.statement.select.Select;
import net.sf.jsqlparser.util.TablesNamesFinder;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.util.List;
import java.util.Set;

@Slf4j
@SpringBootTest
public class ParseSqlTests {

    @Resource
    private DBHelper devcloudDevDBHelper;

    @Test
    public void testParseOneSql() throws Exception {
        String sql = IOUtils.readClasspathResourceAsString("/ola_analysis/test.sql");
        System.out.println(sql);

        sql = handleSql(sql);

        Statement statement = CCJSqlParserUtil.parse(sql);
        System.out.println(statement);
    }

    @Test
    public void testParseProjectSql() throws Exception {

        devcloudDevDBHelper.deleteHard(OlaTableDependenceDO.class, "where 1=1");
        devcloudDevDBHelper.deleteHard(OlaTableInfoDO.class, "where 1=1");

        String dir = "D:\\code-personal\\tcres-dwh-nick";
        List<File> sqlFiles = IOUtils.listFiles(new File(dir), "\\.sql");

        ListUtils.forEach(sqlFiles, file -> {
            String sql = "";
            try {
                sql = IOUtils.readAllAndClose(new FileInputStream(file), "utf-8");
            } catch (FileNotFoundException e) {
                log.error("read file {} error", file, e);
                return;
            }

            System.out.println("Parsing file: " + file.getAbsolutePath());

            try {
                Statement statement = CCJSqlParserUtil.parse(handleSql(sql));

                System.out.println(statement);

                if (statement instanceof Insert insert) {
                    Select select = insert.getSelect();
                    if (select != null) {
                        System.out.println(select);
                        // 创建 TablesNamesFinder 实例
                        TablesNamesFinder tablesNamesFinder = new TablesNamesFinder();

                        // 获取所有表名
                        Set<String> tableList = tablesNamesFinder.getTables((Statement) select);
                        System.out.println(tableList);

                        List<OlaTableDependenceDO> list = ListUtils.transform(tableList, o -> {
                            OlaTableDependenceDO dependenceDO = new OlaTableDependenceDO();
                            dependenceDO.setTableName(insert.getTable().getFullyQualifiedName());
                            dependenceDO.setDependentTableName(o);
                            return dependenceDO;
                        });

                        // 去掉自己依赖自己的
                        list = ListUtils.filter(list, o -> !o.getTableName().equals(o.getDependentTableName()));

                        devcloudDevDBHelper.insertBatchWithoutReturnId(list);
                    } else {
                        throw new RuntimeException("insert no select");
                    }

                    OlaTableInfoDO one = devcloudDevDBHelper.getOne(OlaTableInfoDO.class, "where table_full_name=?", insert.getTable().getFullyQualifiedName());
                    if (one == null) {
                        one = new OlaTableInfoDO();
                        one.setTableFullName(insert.getTable().getFullyQualifiedName());
                        one.setDatabaseName(insert.getTable().getSchemaName());
                        one.setTableName(insert.getTable().getName());
                        one.setInsertSql(sql);
                        devcloudDevDBHelper.insert(one);
                    }
                } else if (statement instanceof CreateTable ) {
                   // TODO save create table sql
                } else {
                    throw new RuntimeException("not support statement type: " + statement.getClass().getName());
                }
            } catch (JSQLParserException e) {
                log.error("parse file {} error", file, e);
            }
        });
    }


    private String handleSql(String sql) {

        // 移除set ; 设置
        sql = sql.replaceAll("set .*;", "");

        // 把${YYYYMMDD+1}00 这种后面的00去掉，什么奇葩写法
        sql = sql.replace("}00", "}");

        // 特别处理CONCAT_WS('-' , ${YYYY} , ${MM} , '01'))
        sql = sql.replace("CONCAT_WS('-' , ${YYYY} , ${MM} , '01')", "'${YYYY}-${MM}-01'");

        // 处理${xxx}变量
        sql = addQuotesToUnquotedPlaceholders(sql);

        // 处理符号::
        sql = sql.replaceAll("::", ".");

        // 处理一些特别的用法
        sql = sql.replaceAll("(?i)" + "PARTITION default", "PARTITION 'default'"); // 不区分大小写的替换
        sql = sql.replaceAll("(?i)" + "STORED AS ORCFILE COMPRESS", "");

        // 特别处理start转义
        sql = sql.replace(" start ", " `start` ");

        // 特别处理.output转义
        sql = sql.replace(".output ", ".`output` ");

        sql = sql.replace("refresh datasource mysql_cloud_demand;", ""); // 去掉乱七八糟的
        sql = sql.replace("==", "="); // 这个主要出现在if子句里，也是修复不规范sql

        return sql;
    }

    /**
     * 将字符串中不在单引号内的${xxxx}模式的子字符串添加单引号
     *
     * @param input 输入字符串
     * @return 处理后的字符串
     */
    public static String addQuotesToUnquotedPlaceholders(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }

        StringBuilder result = new StringBuilder();
        int length = input.length();
        boolean inSingleQuotes = false;

        for (int i = 0; i < length; ) {
            char currentChar = input.charAt(i);

            // 处理单引号状态
            if (currentChar == '\'') {
                // 检查是否是转义的单引号
                if (i > 0 && input.charAt(i - 1) == '\\') {
                    result.append(currentChar);
                } else {
                    inSingleQuotes = !inSingleQuotes;
                    result.append(currentChar);
                }
                i++;
                continue;
            }

            // 如果当前字符是$且后面跟着{
            if (currentChar == '$' && i + 1 < length && input.charAt(i + 1) == '{') {
                // 查找结束的}
                int end = input.indexOf('}', i + 2);
                if (end == -1) {
                    // 没有找到对应的}，直接添加剩余部分
                    result.append(input.substring(i));
                    break;
                } else {
                    String placeholder = input.substring(i, end + 1);
                    if (!inSingleQuotes) {
                        // 添加单引号包裹
                        result.append('\'').append(placeholder).append('\'');
                    } else {
                        // 已经在单引号内，直接添加
                        result.append(placeholder);
                    }
                    i = end + 1;
                    continue;
                }
            }

            // 其他字符直接添加
            result.append(currentChar);
            i++;
        }

        return result.toString();
    }

}
