package com.pugwoo.dboperate.ola_analysis;

import com.pugwoo.dbhelper.annotation.RelatedColumn;
import lombok.Data;

import java.util.List;

@Data
public class OlaTableDependenceVO extends OlaTableDependenceDO {

    @RelatedColumn(localColumn = "dependent_table_name", remoteColumn = "table_name")
    private List<OlaTableDependenceVO> dependents;

    @RelatedColumn(localColumn = "table_name", remoteColumn = "table_full_name")
    private OlaTableInfoDO olaTableInfoDO;

}
