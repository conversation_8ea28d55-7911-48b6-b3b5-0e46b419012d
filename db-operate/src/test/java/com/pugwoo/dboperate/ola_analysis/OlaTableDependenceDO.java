package com.pugwoo.dboperate.ola_analysis;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * ola表依赖表关系
 */
@Data
@ToString
@Table("ola_table_dependence")
public class OlaTableDependenceDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Integer id;

    /** 软删除，0未删，1已删<br/>Column: [deleted] */
    @Column(value = "deleted", softDelete = {"0", "1"})
    private Integer deleted;

    /** 创建时间<br/>Column: [create_time] */
    @Column(value = "create_time", setTimeWhenInsert = true)
    private LocalDateTime createTime;

    /** 修改时间<br/>Column: [update_time] */
    @Column(value = "update_time", setTimeWhenInsert = true, setTimeWhenUpdate = true)
    private LocalDateTime updateTime;

    /** 主表，这里是全称：数据库.表名<br/>Column: [table_name] */
    @Column(value = "table_name")
    private String tableName;

    /** 依赖的表，这里是全称：数据库.表名<br/>Column: [dependent_table_name] */
    @Column(value = "dependent_table_name")
    private String dependentTableName;

    /** 依赖的类型，预留用，例如select、join、subquery等<br/>Column: [type] */
    @Column(value = "type")
    private String type;

}