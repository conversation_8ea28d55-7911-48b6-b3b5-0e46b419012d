package com.pugwoo.dboperate.config;

import com.pugwoo.dbhelper.DBHelper;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;

/**
 * Devcloud 开发环境和生产环境数据库配置
 */
@Configuration
public class DevcloudConfiguration {

    // devcloud dev

    @Bean(name = "devcloudDevDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.devcloud-dev")
    public DataSource devcloudDevDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean("devcloudDevDBHelper")
    public DBHelper devcloudDevDBHelper(@Qualifier("devcloudDevDataSource") DataSource dataSource) {
        return DBConfigCommonTools.newDBHelper(dataSource);
    }

    // devcloud idc

    @Bean(name = "devcloudIdcDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.devcloud-idc")
    public DataSource devcloudIdcDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean("devcloudIdcDBHelper")
    public DBHelper devcloudIdcDBHelper(@Qualifier("devcloudIdcDataSource") DataSource dataSource) {
        return DBConfigCommonTools.newDBHelper(dataSource);
    }
}
