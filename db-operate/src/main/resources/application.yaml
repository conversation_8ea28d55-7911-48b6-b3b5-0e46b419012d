mysql-default-args: useSSL=false&serverTimezone=GMT%2B8&useUnicode=true&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&rewriteBatchedStatements=TRUE&useCursorFetch=true

spring:
  datasource:
    clickhouse-yunti-dev-cloud-demand:
      jdbc-url: ************************************************
      username: test
      password: app@ERP2018
      driver-class-name: com.clickhouse.jdbc.ClickHouseDriver
    ck-cloud-demand-new-idc:
      jdbc-url: *************************************************************************
      username: crp_root
      password: app@ERP2018
      driver-class-name: com.clickhouse.jdbc.ClickHouseDriver
    ck-std-crp-new-idc:
      jdbc-url: ********************************************************************
      username: crp_root
      password: app@ERP2018
      driver-class-name: com.clickhouse.jdbc.ClickHouseDriver
    ck-forecast-research-new-idc:
      jdbc-url: ******************************************************************************
      username: crp_root
      password: app@ERP2018
      driver-class-name: com.clickhouse.jdbc.ClickHouseDriver
    ck-yunti-demand-new-idc:
      jdbc-url: *************************************************************************
      username: crp_root
      password: app@ERP2018
      driver-class-name: com.clickhouse.jdbc.ClickHouseDriver
    ck-std-crp-dev:
      jdbc-url: ******************************************************************
      username: test
      password: app@ERP2018
      driver-class-name: com.clickhouse.jdbc.ClickHouseDriver
    ck-cubes-idc:
      jdbc-url: ****************************************************************
      username: default
      password: app@ERP2018
      driver-class-name: com.clickhouse.jdbc.ClickHouseDriver
    yunti-idc: # 生产环境的云梯数据库（主库）
      jdbc-url: *************************************?${mysql-default-args}
      username: yunti_demand
      password: Xqeyxwc2
      driver-class-name: com.mysql.cj.jdbc.Driver
    demand-idc: # 生产环境的cvm需求预测
      jdbc-url: *******************************************?${mysql-default-args}
      username: root
      password: app@ERP2018
      driver-class-name: com.mysql.cj.jdbc.Driver
    cloud-demand-dev:
      jdbc-url: *********************************************?${mysql-default-args}
      username: test
      password: test_test
      driver-class-name: com.mysql.cj.jdbc.Driver
    cloud-demand-idc:
      jdbc-url: *******************************************?${mysql-default-args}
      username: yunti_apply
      password: yunti_apply
      driver-class-name: com.mysql.cj.jdbc.Driver
    cloud-demand-common-dev:
      jdbc-url: *********************************************_common?${mysql-default-args}
      username: test
      password: test_test
      driver-class-name: com.mysql.cj.jdbc.Driver
    cloud-demand-common-idc:
      jdbc-url: *******************************************_common?${mysql-default-args}
      username: yunti_apply
      password: yunti_apply
      driver-class-name: com.mysql.cj.jdbc.Driver
    cloud-demand-lab-dev:
      jdbc-url: *********************************************_lab?${mysql-default-args}
      username: test
      password: test_test
      driver-class-name: com.mysql.cj.jdbc.Driver
    cloud-demand-lab-idc:
      jdbc-url: ***********************************************?${mysql-default-args}
      username: yunti_apply
      password: yunti_apply
      driver-class-name: com.mysql.cj.jdbc.Driver
    cloud-demand-data-dev:
      jdbc-url: *********************************************_data?${mysql-default-args}
      username: test
      password: test_test
      driver-class-name: com.mysql.cj.jdbc.Driver
    cloud-demand-data-idc:
      jdbc-url: **********************************************?${mysql-default-args}
      username: crp_rw
      password: crp_rw@2025
      driver-class-name: com.mysql.cj.jdbc.Driver
    rrp-idc: # 资源中台rrp的生产数据库
      jdbc-url: ******************************************?${mysql-default-args}
      username: cloud_demand
      password: z8Z_4ke5bAs_gzS
      driver-class-name: com.mysql.cj.jdbc.Driver
    jxc-txy-idc: # 进销存（腾讯云）数据库
      jdbc-url: *********************************?${mysql-default-args}
      username: galaxyro
      password: OQ@boVA2^YcD!4
      driver-class-name: com.mysql.cj.jdbc.Driver
    mck-idc: #麦肯锡要的数据
      jdbc-url: ********************************?${mysql-default-args}
      username: root
      password: app@ERP2018
      driver-class-name: com.mysql.cj.jdbc.Driver
    shuttle-idc: # 资源中台shuttle的生产数据库
      jdbc-url: *********************************************?${mysql-default-args}
      username: shuttle
      password: "TscM2019@#cloud"
      driver-class-name: com.mysql.cj.jdbc.Driver
    erp-idc:
      jdbc-url: ******************************************?${mysql-default-args}
      username: resdb
      password: "resdb_resdb"
      driver-class-name: com.mysql.cj.jdbc.Driver
    erp-resplan-idc:
      jdbc-url: ****************************************?${mysql-default-args}
      username: resdb
      password: "resdb_resdb"
      driver-class-name: com.mysql.cj.jdbc.Driver
    obs-idc: # obs的生产数据库
      jdbc-url: ********************************************?${mysql-default-args}
      username: obsdb
      password: obsdb
      driver-class-name: com.mysql.cj.jdbc.Driver
    devcloud-dev:
      jdbc-url: *********************************************?${mysql-default-args}
      username: root
      password: Sz118229
      driver-class-name: com.mysql.cj.jdbc.Driver
    devcloud-idc:
      jdbc-url: *********************************************?${mysql-default-args}
      username: root
      password: Sz118229
      driver-class-name: com.mysql.cj.jdbc.Driver